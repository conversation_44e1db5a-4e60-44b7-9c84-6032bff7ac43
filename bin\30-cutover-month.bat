@echo off
REM 设置UTF-8编码以正确显示中文
chcp 65001 >nul 2>nul
setlocal ENABLEDELAYEDEXPANSION
call "%~dp0\01-config.bat"
set "MONTHS_TODO=%BASE_DIR%months_to_migrate.txt"
if not exist "%MONTHS_TODO%" (
  echo 未找到 %MONTHS_TODO% ，请先运行 10-list-months.bat 并编辑 months_to_migrate.txt
  exit /b 1
)

REM 停服务（可选）
if not "%SERVICE_NAME%"=="" (
  echo 尝试停止服务：%SERVICE_NAME%
  if %DRY_RUN%==0 net stop "%SERVICE_NAME%"
)

echo 即将执行最终差异同步與切换，请确保业务已停止写入。

for /f "usebackq delims=" %%M in ("%MONTHS_TODO%") do (
  set "MONTH=%%~M"
  if exist "%SOURCE_ROOT%\!MONTH!" (
    call :PickTarget !MONTH! TARGET DRIVE
    if not "!TARGET!"=="" (
      if not exist "!TARGET!\!MONTH!" (
        echo 创建目标目录：!TARGET!\!MONTH!
        if %DRY_RUN%==0 mkdir "!TARGET!\!MONTH!"
      )

      set "LOGFILE=%LOG_DIR%\final_!MONTH!.log"
      set "CMD1=robocopy \"%SOURCE_ROOT%\!MONTH!\" \"!TARGET!\!MONTH!\" /MIR /COPY:DATSO /DCOPY:DAT /R:%ROBO_RETRY% /W:%ROBO_WAIT% /MT:%THREADS% /FFT /XJ /TEE /LOG+:\"!LOGFILE!\""
      echo [FINAL] !CMD1!
      if %DRY_RUN%==0 call cmd /c "!CMD1!"

      if exist "%SOURCE_ROOT%\!MONTH!_old" (
        echo 发现残留 _old 目录，先行删除或人工处理后重试：%SOURCE_ROOT%\!MONTH!_old
      ) else (
        echo 重命名原目录為 _old：%SOURCE_ROOT%\!MONTH! -> !MONTH!_old
        if %DRY_RUN%==0 ren "%SOURCE_ROOT%\!MONTH!" "!MONTH!_old"

        echo 创建联接：%SOURCE_ROOT%\!MONTH!  ->  !TARGET!\!MONTH!
        set "CMD2=mklink /J \"%SOURCE_ROOT%\!MONTH!\" \"!TARGET!\!MONTH!\""
        echo [LINK] !CMD2!
        if %DRY_RUN%==0 call cmd /c "!CMD2!"

        REM 验证：若 /L 下 robocopy 无需复制则认为一致
        set "VLOG=%LOG_DIR%\verify_!MONTH!.log"
        set "CMD3=robocopy \"%SOURCE_ROOT%\!MONTH!\" \"!TARGET!\!MONTH!\" /L /NJH /NJS /NP /NDL /NFL /R:0"
        echo [VERIFY] !CMD3!  ^> !VLOG!
        if %DRY_RUN%==0 call cmd /c "!CMD3! > \"!VLOG!\""

        REM 抽样哈希验证（可选）
        if %VERIFY_SAMPLE_COUNT% GTR 0 (
          where certutil >nul 2>nul && (
            echo [HASH] 抽样 %VERIFY_SAMPLE_COUNT% 个文件进行 certutil 校验...
            call :HashSample "%SOURCE_ROOT%\!MONTH!" %VERIFY_SAMPLE_COUNT% "!MONTH!"
          )
        )

        if %DELETE_OLD%==1 (
          echo 删除旧目录：%SOURCE_ROOT%\!MONTH!_old
          if %DRY_RUN%==0 rmdir /S /Q "%SOURCE_ROOT%\!MONTH!_old"
        ) else (
          echo 已保留旧目录：%SOURCE_ROOT%\!MONTH!_old  （回滚窗口期内可随时恢复）
        )
      )
    ) else (
      echo 未找到可用目标盘，跳过  !MONTH!
    )
  ) else (
    echo 跳过：源目录不存在  %SOURCE_ROOT%\!MONTH!
  )
)

REM 启服务（可选）
if not "%SERVICE_NAME%"=="" (
  echo 尝试启动服务：%SERVICE_NAME%
  if %DRY_RUN%==0 net start "%SERVICE_NAME%"
)

echo 切换完成。请检查应用日志、磁盘空间与备份任务。
exit /b 0

:PickTarget
REM %1=MONTH 输出：TARGET, DRIVE(E/F)
setlocal ENABLEDELAYEDEXPANSION
set "m=%~1"
set "drive="
set "target="

call :FreeGB E: egb
call :FreeGB F: fgb

if /I "%STRATEGY%"=="BALANCE_BY_MONTH" (
  for /f "tokens=1,2 delims=-" %%a in ("!m!") do set mm=%%b
  REM 强制使用十进制，避免08、09等被解释为八进制
  REM 先去掉前导零，再计算模数
  set /a mm_num=1!mm! - 100
  set /a mod=!mm_num! %% 2
  if !mod! EQU 1 (
    if !egb! GTR %TARGET_E_MIN_FREE_GB% (set drive=E: & set target=%TARGET_E%) else if !fgb! GTR %TARGET_F_MIN_FREE_GB% (set drive=F: & set target=%TARGET_F%)
  ) else (
    if !fgb! GTR %TARGET_F_MIN_FREE_GB% (set drive=F: & set target=%TARGET_F%) else if !egb! GTR %TARGET_E_MIN_FREE_GB% (set drive=E: & set target=%TARGET_E%)
  )
) else (
  if !egb! GTR %TARGET_E_MIN_FREE_GB% (set drive=E: & set target=%TARGET_E%) else if !fgb! GTR %TARGET_F_MIN_FREE_GB% (set drive=F: & set target=%TARGET_F%)
)

endlocal & set "%2=%target%" & set "%3=%drive%"
exit /b 0

:FreeGB
REM %1=Drive(如 E:), %2=变量名
setlocal ENABLEDELAYEDEXPANSION
set "fb="
for /f "tokens=2 delims==" %%A in ('wmic logicaldisk where "DeviceID='%1'" get FreeSpace /value ^| find "FreeSpace"') do (
  set "fb=%%A"
  REM 去除可能的空白字符
  set "fb=!fb: =!"
)
REM 使用PowerShell计算GB，避免32位整数溢出
if defined fb if not "!fb!"=="" (
  REM 检查是否为纯数字
  echo !fb!| findstr /r "^[0-9][0-9]*$" >nul
  if !errorlevel! equ 0 (
    REM 使用PowerShell进行大数字除法运算
    for /f %%G in ('powershell -command "[math]::Floor(!fb!/1073741824)"') do set gb=%%G
  ) else (
    set gb=0
  )
) else (
  set gb=0
)
endlocal & set "%2=%gb%"
exit /b 0

:HashSample
REM %1=LinkPath %2=Count %3=Month
setlocal ENABLEDELAYEDEXPANSION
set "lp=%~1"
set /a cnt=%2
set "m=%~3"
set "out=%LOG_DIR%\hash_!m!.log"
>"!out!" (echo 抽样路径：!lp!)
for /f "usebackq delims=" %%F in (`dir /b /s /a:-d "!lp!" ^| sort`) do (
  if !cnt! LEQ 0 goto :done
  echo 校验：%%F >>"!out!"
  certutil -hashfile "%%F" SHA256 >>"!out!"
  set /a cnt-=1
)
:done
endlocal & exit /b 0