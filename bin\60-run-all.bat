@echo off
setlocal ENABLEDELAYEDEXPANSION
call "%~dp0\01-config.bat"

REM 第1步：列出月份并准备清单
call "%~dp0\10-list-months.bat" || (echo 生成月份清单失败 & exit /b 1)

echo.
echo ========= 第2步：预同步（可重复执行，多次运行提升增量效率） =========
call "%~dp0\20-pre-sync.bat" || (echo 预同步失败，请检查日志 %LOG_DIR% & exit /b 1)

echo.
echo ========= 第3步：准备维护窗口 =========
echo - 请确认已通知业务方，准备停机。
echo - 当前为 DRY_RUN=%DRY_RUN% （1=演练，0=执行）。
choice /M "是否继续执行切换脚本 (Y/N)?"
if errorlevel 2 (echo 已取消 & exit /b 0)

REM 第4步：执行切换
call "%~dp0\30-cutover-month.bat" || (echo 切换失败，请检查日志 %LOG_DIR% & exit /b 1)

echo.
echo ========= 完成 =========
echo - 请检查应用功能、日志与磁盘空间。
echo - 若稳定运行数日后，可将 DELETE_OLD=1 再次运行切换脚本以清理 _old 目录。
exit /b 0