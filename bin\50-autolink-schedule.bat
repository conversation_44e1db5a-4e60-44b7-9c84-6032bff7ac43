@echo off
setlocal ENABLEDELAYEDEXPANSION
call "%~dp0\01-config.bat"

set "CREATE_LINK=%~dp0create_current_month_link.bat"
>
"%CREATE_LINK%" (
  echo @echo off
  echo setlocal ENABLEDELAYEDEXPANSION
  echo call "%%~dp0\01-config.bat"
  echo for /f "delims=" %%%%d in ('powershell -NoProfile -Command ^(Get-Date^).ToString^('yyyy-MM'^)') do set MM=%%%%d
  echo rem 计算 E/F 可用空间(GB)
  echo for /f "tokens=2 delims==" %%%%A in ('wmic logicaldisk where "DeviceID='E:'" get FreeSpace /value ^^^| find "FreeSpace"') do set EFB=%%%%A
  echo for /f "tokens=2 delims==" %%%%A in ('wmic logicaldisk where "DeviceID='F:'" get FreeSpace /value ^^^| find "FreeSpace"') do set FFB=%%%%A
  echo set /a EGB=%%EFB%%/1024/1024/1024
  echo set /a FGB=%%FFB%%/1024/1024/1024
  echo set "TARGET="
  echo if /I "%%STRATEGY%%"=="BALANCE_BY_MONTH" ^(
  echo   for /f "tokens=1,2 delims=-" %%%%a in ("%%MM%%") do set m2=%%%%b
  echo   set /a mod=%%m2%% %% 2
  echo   if %%mod%% EQU 1 ^( if %%EGB%% GTR %%TARGET_E_MIN_FREE_GB%% ^(set "TARGET=%%TARGET_E%%"^) else if %%FGB%% GTR %%TARGET_F_MIN_FREE_GB%% ^(set "TARGET=%%TARGET_F%%"^) ^) else ^( if %%FGB%% GTR %%TARGET_F_MIN_FREE_GB%% ^(set "TARGET=%%TARGET_F%%"^) else if %%EGB%% GTR %%TARGET_E_MIN_FREE_GB%% ^(set "TARGET=%%TARGET_E%%"^) ^)
  echo ^) else ^(
  echo   if %%EGB%% GTR %%TARGET_E_MIN_FREE_GB%% ^(set "TARGET=%%TARGET_E%%"^) else if %%FGB%% GTR %%TARGET_F_MIN_FREE_GB%% ^(set "TARGET=%%TARGET_F%%"^)
  echo ^)
  echo if "%%TARGET%%"=="" ^( echo 无可用目标盘 ^& exit /b 1 ^)
  echo if not exist "%%TARGET%%\%%MM%%" mkdir "%%TARGET%%\%%MM%%"
  echo mklink /J "%%SOURCE_ROOT%%\%%MM%%" "%%TARGET%%\%%MM%%"
  echo exit /b 0
)

echo 已生成：%CREATE_LINK%

echo 创建每月计划任务（每月1日 00:05，最高权限）：
schtasks /Create /TN "CreateMonthlyLink-DataPkg" /TR "\"%CREATE_LINK%\"" /SC MONTHLY /D 1 /ST 00:05 /RL HIGHEST /F

echo 完成。可在 计划任务程序 中检查任务条目。
exit /b 0