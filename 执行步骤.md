# 方案C（按月目录联接到 E/F）——傻瓜式执行步骤与一键脚本

适用环境：Windows Server 2008 R2（离线）、项目目录 `D:\DataPkgFile`，按月目录结构（`yyyy-MM`）。目标是在不改业务代码和备份路径的前提下，将历史月份迁移到 E/F 大盘，并在 `D:\DataPkgFile\yyyy-MM` 上创建 NTFS 目录联接（Junction），从而释放 D 盘空间。

重要说明：
- 全流程默认不修改备份任务（仍从 `D:\DataPkgFile` 备份到 `G:\DataPkgFile`）。请确认备份使用 Robocopy 时未启用 `/SL` 参数，以确保实际内容被备份。
- 所有脚本均需“以管理员身份运行”。
- 建议先选取一个较小月份试点，验证通过后再批量推进。

---

## 0. 目录规划与术语

- 源目录（应用读写不变）：`D:\DataPkgFile`
- 目标挂载目录：
  - 优先：`E:\DataPkgFile`
  - 其次：`F:\DataPkgFile`
- 月份目录示例：`D:\DataPkgFile\2022-07`
- 迁移后：`D:\DataPkgFile\2022-07` 将被替换为指向 `E:\DataPkgFile\2022-07` 或 `F:\DataPkgFile\2022-07` 的“目录联接（Junction）”。

---

## 1. 一键脚本概览（开箱即用）

本文提供一套“可一键执行”的批处理脚本框架，包含：
- 01-config.bat：全局配置
- 10-list-months.bat：生成月份清单（含兼容 PowerShell 与 CMD 双方案）
- 20-pre-sync.bat：不停机“预同步”（多次可重复执行）
- 30-cutover-month.bat：维护窗口内“最终差异同步 + 切换为联接 + 验证 + 可选删除旧目录”
- 40-rollback-month.bat：遇故障的回滚脚本
- 50-autolink-schedule.bat：创建“每月首日自动联接”的计划任务（可选）
- 60-run-all.bat：一键编排（从“列月→预同步→提示停机→切换→提示启动”）

脚本特点：
- 兼容 Windows Server 2008 R2，默认依赖 CMD；如有 PowerShell（v2+），将自动利用增强能力（排序、日期处理更精确）。
- DRY_RUN 安全开关默认开启（只打印将执行的命令，不落地变更）。需要真正执行时，将其改为 0。
- 具备“保留磁盘余量阈值”的判断，优先填满 E 盘后再用 F 盘，或按“奇偶月均衡”策略分配。

脚本目录建议：`C:\DataPkg-Migrate\`
- 放置脚本：`C:\DataPkg-Migrate\bin\*.bat`
- 日志目录：`C:\DataPkg-Migrate\logs\`

---

## 2. 快速开始（极简操作顺序）

1) 以管理员身份打开 CMD。
2) 创建目录并保存脚本（按下文代码片段保存为 .bat 文件）：
   - `C:\DataPkg-Migrate\bin\01-config.bat`
   - `C:\DataPkg-Migrate\bin\10-list-months.bat`
   - `C:\DataPkg-Migrate\bin\20-pre-sync.bat`
   - `C:\DataPkg-Migrate\bin\30-cutover-month.bat`
   - `C:\DataPkg-Migrate\bin\40-rollback-month.bat`
   - `C:\DataPkg-Migrate\bin\50-autolink-schedule.bat`
   - `C:\DataPkg-Migrate\bin\60-run-all.bat`
3) 编辑 `01-config.bat`：
   - 设置 `SERVICE_NAME`（示例：`Tomcat8.5`），若无服务名，可留空并手动停机/启动。
   - 选择分盘策略：`STRATEGY=FILL_E_THEN_F` 或 `STRATEGY=BALANCE_BY_MONTH`。
   - 设置 `DRY_RUN=1`（先演练）；正式切换前改为 `DRY_RUN=0`。
4) 运行列月：`C:\DataPkg-Migrate\bin\10-list-months.bat`
   - 生成 `months.txt` 和（尽力生成）`months_to_migrate.txt`（默认排除最近2个月）。
   - 若 `months_to_migrate.txt` 未自动生成，请人工从 `months.txt` 复制需要迁移的月份到 `months_to_migrate.txt`。
5) 多次运行预同步（可在业务运行时反复执行）：`C:\DataPkg-Migrate\bin\20-pre-sync.bat`
6) 维护窗口内：
   - 停服务（或允许脚本帮你停）：
   - 将 `DRY_RUN=0` 后运行：`C:\DataPkg-Migrate\bin\30-cutover-month.bat`
   - 切换完成后，启动服务。
7) 验证：应用功能、日志、磁盘使用、备份任务。
8) 稳定后再考虑删除 `_old` 目录（`DELETE_OLD=1`），以释放 D 盘空间。

---

## 3. 注意事项与安全边界

- 管理员权限：`mklink /J` 及部分系统命令需要管理员。
- 备份任务：保持从 D 盘备份；严禁在备份脚本中使用 `/SL`。
- 谨慎使用 `/MIR`：仅在维护窗口、停机后执行最终差异同步；预同步阶段使用 `/E`。
- 磁盘余量：脚本默认留足安全余量（可配置），当 E 盘不足时自动切换到 F 盘。
- 当前/上月：建议不迁移当前月与上月（数据写入频繁），脚本默认尝试排除最近 2 个月。
- 验证通过，删除 `_old`：删除不可逆，建议在运行 1-7 天稳定后再删除。

---

## 4. 脚本代码清单

将以下代码分别保存到对应路径（ANSI/GBK 或 UTF-8 无 BOM 编码，避免中文路径转义问题）。

### 4.1 01-config.bat（全局配置）

```bat
@echo off
setlocal ENABLEDELAYEDEXPANSION
REM =================== 全局配置 ===================
REM 源与目标目录
set "SOURCE_ROOT=D:\DataPkgFile"
set "TARGET_E=E:\DataPkgFile"
set "TARGET_F=F:\DataPkgFile"

REM 脚本与日志目录
set "SCRIPT_DIR=%~dp0"
set "BASE_DIR=%SCRIPT_DIR%..\"
set "LOG_DIR=C:\DataPkg-Migrate\logs"

REM 服务名（可选）：如 Tomcat8.5；若无则留空
set "SERVICE_NAME=Tomcat8.5"

REM 分配策略：FILL_E_THEN_F 或 BALANCE_BY_MONTH
set "STRATEGY=FILL_E_THEN_F"

REM 预留容量（GB），低于该值停止向该盘写入
set "TARGET_E_MIN_FREE_GB=150"
set "TARGET_F_MIN_FREE_GB=120"

REM 预同步排除最近 N 个月
set "EXCLUDE_RECENT_MONTHS=2"

REM 多线程与重试
set "THREADS=16"
set "ROBO_RETRY=1"
set "ROBO_WAIT=2"

REM 安全开关：1=演练（只打印命令），0=执行
set "DRY_RUN=1"

REM 切换后是否删除 _old：1=删除，0=保留
set "DELETE_OLD=0"

REM 抽样验证（每月抽 N 个文件哈希校验，可设 0 跳过）
set "VERIFY_SAMPLE_COUNT=5"

if not exist "%LOG_DIR%" mkdir "%LOG_DIR%" >nul 2>nul
exit /b 0
```

### 4.2 10-list-months.bat（生成月份清单）

```bat
@echo off
setlocal ENABLEDELAYEDEXPANSION
call "%~dp0\01-config.bat"

set "MONTHS_ALL=%BASE_DIR%months.txt"
set "MONTHS_TODO=%BASE_DIR%months_to_migrate.txt"

REM 先用 PowerShell（若存在）精准生成并排除最近 N 个月
where powershell >nul 2>nul
if %errorlevel%==0 (
  echo 使用 PowerShell 生成月份清单...
  powershell -NoProfile -Command ^
    "$src='%SOURCE_ROOT%';$n=%EXCLUDE_RECENT_MONTHS%;" ^
    
    
    
    
    
    
    "Get-ChildItem -Directory $src | Where-Object { $_.Name -match '^\d{4}-\d{2}$' } | Sort-Object Name | Select-Object -ExpandProperty Name | Tee-Object '%MONTHS_ALL%' | Select-Object -SkipLast $n | Out-File '%MONTHS_TODO%' -Encoding ASCII"
) else (
  echo 未检测到 PowerShell，退化到 CMD 方式（僅生成 months.txt）。
  dir /ad /b "%SOURCE_ROOT%" | findstr /r "^[0-9][0-9][0-9][0-9]-[0-1][0-9]$" > "%MONTHS_ALL%"
  if not exist "%MONTHS_TODO%" (
    echo 请人工编辑 %MONTHS_TODO% ，从 %MONTHS_ALL% 复制需要迁移的月份（建议排除最近 %EXCLUDE_RECENT_MONTHS% 个月）。
    copy "%MONTHS_ALL%" "%MONTHS_TODO%" >nul
  )
)

echo 已生成：
for %%f in ("%MONTHS_ALL%","%MONTHS_TODO%") do if exist %%~f echo  ^> %%~f

echo. & echo 请检查 months_to_migrate.txt 是否符合预期。
exit /b 0
```

### 4.3 20-pre-sync.bat（不停机预同步，可反复执行）

```bat
@echo off
setlocal ENABLEDELAYEDEXPANSION
call "%~dp0\01-config.bat"

set "MONTHS_TODO=%BASE_DIR%months_to_migrate.txt"
if not exist "%MONTHS_TODO%" (
  echo 未找到 %MONTHS_TODO% ，请先运行 10-list-months.bat 并编辑 months_to_migrate.txt
  exit /b 1
)

for /f "usebackq delims=" %%M in ("%MONTHS_TODO%") do (
  set "MONTH=%%~M"
  if exist "%SOURCE_ROOT%\!MONTH!" (
    call :PickTarget !MONTH! TARGET DRIVE
    if not "!TARGET!"=="" (
      if not exist "!TARGET!\!MONTH!" (
        echo 创建目标目录：!TARGET!\!MONTH!
        if %DRY_RUN%==0 mkdir "!TARGET!\!MONTH!"
      )
      set "LOGFILE=%LOG_DIR%\pre_!MONTH!.log"
      set "CMD=robocopy \"%SOURCE_ROOT%\!MONTH!\" \"!TARGET!\!MONTH!\" /E /COPY:DATSO /DCOPY:DAT /R:%ROBO_RETRY% /W:%ROBO_WAIT% /MT:%THREADS% /FFT /XJ /TEE /LOG+:\"!LOGFILE!\""
      echo [PRE] !CMD!
      if %DRY_RUN%==0 call cmd /c "!CMD!"
    ) else (
      echo 未找到可用目标盘，跳过  !MONTH!
    )
  ) else (
    echo 跳过：源目录不存在  %SOURCE_ROOT%\!MONTH!
  )
)

echo 预同步完成（可多次重复执行）。
exit /b 0

:PickTarget
REM %1=MONTH 输出：TARGET, DRIVE(E/F)
setlocal ENABLEDELAYEDEXPANSION
set "m=%~1"
set "drive="
set "target="

call :FreeGB E: egb
call :FreeGB F: fgb

if /I "%STRATEGY%"=="BALANCE_BY_MONTH" (
  for /f "tokens=1,2 delims=-" %%a in ("!m!") do set mm=%%b
  set /a mod=!mm! %% 2
  if !mod! EQU 1 (
    if !egb! GTR %TARGET_E_MIN_FREE_GB% (set drive=E: & set target=%TARGET_E%) else if !fgb! GTR %TARGET_F_MIN_FREE_GB% (set drive=F: & set target=%TARGET_F%)
  ) else (
    if !fgb! GTR %TARGET_F_MIN_FREE_GB% (set drive=F: & set target=%TARGET_F%) else if !egb! GTR %TARGET_E_MIN_FREE_GB% (set drive=E: & set target=%TARGET_E%)
  )
) else (
  if !egb! GTR %TARGET_E_MIN_FREE_GB% (set drive=E: & set target=%TARGET_E%) else if !fgb! GTR %TARGET_F_MIN_FREE_GB% (set drive=F: & set target=%TARGET_F%)
)

endlocal & set "%2=%target%" & set "%3=%drive%"
exit /b 0

:FreeGB
REM %1=Drive(如 E:), %2=变量名
for /f "tokens=2 delims==" %%A in ('wmic logicaldisk where "DeviceID='%1'" get FreeSpace /value ^| find "FreeSpace"') do set fb=%%A
set /a gb=%fb%/1024/1024/1024
set "%2=%gb%"
exit /b 0
```

### 4.4 30-cutover-month.bat（维护窗口切换）

```bat
@echo off
setlocal ENABLEDELAYEDEXPANSION
call "%~dp0\01-config.bat"
set "MONTHS_TODO=%BASE_DIR%months_to_migrate.txt"
if not exist "%MONTHS_TODO%" (
  echo 未找到 %MONTHS_TODO% ，请先运行 10-list-months.bat 并编辑 months_to_migrate.txt
  exit /b 1
)

REM 停服务（可选）
if not "%SERVICE_NAME%"=="" (
  echo 尝试停止服务：%SERVICE_NAME%
  if %DRY_RUN%==0 net stop "%SERVICE_NAME%"
)

echo 即将执行最终差异同步與切换，请确保业务已停止写入。

for /f "usebackq delims=" %%M in ("%MONTHS_TODO%") do (
  set "MONTH=%%~M"
  if exist "%SOURCE_ROOT%\!MONTH!" (
    call :PickTarget !MONTH! TARGET DRIVE
    if not "!TARGET!"=="" (
      if not exist "!TARGET!\!MONTH!" (
        echo 创建目标目录：!TARGET!\!MONTH!
        if %DRY_RUN%==0 mkdir "!TARGET!\!MONTH!"
      )

      set "LOGFILE=%LOG_DIR%\final_!MONTH!.log"
      set "CMD1=robocopy \"%SOURCE_ROOT%\!MONTH!\" \"!TARGET!\!MONTH!\" /MIR /COPY:DATSO /DCOPY:DAT /R:%ROBO_RETRY% /W:%ROBO_WAIT% /MT:%THREADS% /FFT /XJ /TEE /LOG+:\"!LOGFILE!\""
      echo [FINAL] !CMD1!
      if %DRY_RUN%==0 call cmd /c "!CMD1!"

      if exist "%SOURCE_ROOT%\!MONTH!_old" (
        echo 发现残留 _old 目录，先行删除或人工处理后重试：%SOURCE_ROOT%\!MONTH!_old
      ) else (
        echo 重命名原目录為 _old：%SOURCE_ROOT%\!MONTH! -> !MONTH!_old
        if %DRY_RUN%==0 ren "%SOURCE_ROOT%\!MONTH!" "!MONTH!_old"

        echo 创建联接：%SOURCE_ROOT%\!MONTH!  ->  !TARGET!\!MONTH!
        set "CMD2=mklink /J \"%SOURCE_ROOT%\!MONTH!\" \"!TARGET!\!MONTH!\""
        echo [LINK] !CMD2!
        if %DRY_RUN%==0 call cmd /c "!CMD2!"

        REM 验证：若 /L 下 robocopy 无需复制则认为一致
        set "VLOG=%LOG_DIR%\verify_!MONTH!.log"
        set "CMD3=robocopy \"%SOURCE_ROOT%\!MONTH!\" \"!TARGET!\!MONTH!\" /L /NJH /NJS /NP /NDL /NFL /R:0"
        echo [VERIFY] !CMD3!  ^> !VLOG!
        if %DRY_RUN%==0 call cmd /c "!CMD3! > \"!VLOG!\""

        REM 抽样哈希验证（可选）
        if %VERIFY_SAMPLE_COUNT% GTR 0 (
          where certutil >nul 2>nul && (
            echo [HASH] 抽样 %VERIFY_SAMPLE_COUNT% 个文件进行 certutil 校验...
            call :HashSample "%SOURCE_ROOT%\!MONTH!" %VERIFY_SAMPLE_COUNT% "!MONTH!"
          )
        )

        if %DELETE_OLD%==1 (
          echo 删除旧目录：%SOURCE_ROOT%\!MONTH!_old
          if %DRY_RUN%==0 rmdir /S /Q "%SOURCE_ROOT%\!MONTH!_old"
        ) else (
          echo 已保留旧目录：%SOURCE_ROOT%\!MONTH!_old  （回滚窗口期内可随时恢复）
        )
      )
    ) else (
      echo 未找到可用目标盘，跳过  !MONTH!
    )
  ) else (
    echo 跳过：源目录不存在  %SOURCE_ROOT%\!MONTH!
  )
)

REM 启服务（可选）
if not "%SERVICE_NAME%"=="" (
  echo 尝试启动服务：%SERVICE_NAME%
  if %DRY_RUN%==0 net start "%SERVICE_NAME%"
)

echo 切换完成。请检查应用日志、磁盘空间与备份任务。
exit /b 0

:PickTarget
REM %1=MONTH 输出：TARGET, DRIVE(E/F)
setlocal ENABLEDELAYEDEXPANSION
set "m=%~1"
set "drive="
set "target="

call :FreeGB E: egb
call :FreeGB F: fgb

if /I "%STRATEGY%"=="BALANCE_BY_MONTH" (
  for /f "tokens=1,2 delims=-" %%a in ("!m!") do set mm=%%b
  set /a mod=!mm! %% 2
  if !mod! EQU 1 (
    if !egb! GTR %TARGET_E_MIN_FREE_GB% (set drive=E: & set target=%TARGET_E%) else if !fgb! GTR %TARGET_F_MIN_FREE_GB% (set drive=F: & set target=%TARGET_F%)
  ) else (
    if !fgb! GTR %TARGET_F_MIN_FREE_GB% (set drive=F: & set target=%TARGET_F%) else if !egb! GTR %TARGET_E_MIN_FREE_GB% (set drive=E: & set target=%TARGET_E%)
  )
) else (
  if !egb! GTR %TARGET_E_MIN_FREE_GB% (set drive=E: & set target=%TARGET_E%) else if !fgb! GTR %TARGET_F_MIN_FREE_GB% (set drive=F: & set target=%TARGET_F%)
)

endlocal & set "%2=%target%" & set "%3=%drive%"
exit /b 0

:FreeGB
REM %1=Drive(如 E:), %2=变量名
for /f "tokens=2 delims==" %%A in ('wmic logicaldisk where "DeviceID='%1'" get FreeSpace /value ^| find "FreeSpace"') do set fb=%%A
set /a gb=%fb%/1024/1024/1024
set "%2=%gb%"
exit /b 0

:HashSample
REM %1=LinkPath %2=Count %3=Month
setlocal ENABLEDELAYEDEXPANSION
set "lp=%~1"
set /a cnt=%2
set "m=%~3"
set "out=%LOG_DIR%\hash_!m!.log"
>"!out!" (echo 抽样路径：!lp!)
for /f "usebackq delims=" %%F in (`dir /b /s /a:-d "!lp!" ^| sort`) do (
  if !cnt! LEQ 0 goto :done
  echo 校验：%%F >>"!out!"
  certutil -hashfile "%%F" SHA256 >>"!out!"
  set /a cnt-=1
)
:done
endlocal & exit /b 0
```

### 4.5 40-rollback-month.bat（回滚）

```bat
@echo off
setlocal ENABLEDELAYEDEXPANSION
call "%~dp0\01-config.bat"

if "%~1"=="" (
  echo 用法：40-rollback-month.bat YYYY-MM
  exit /b 1
)
set "MONTH=%~1"

REM 停服务（可选）
if not "%SERVICE_NAME%"=="" (
  echo 尝试停止服务：%SERVICE_NAME%
  if %DRY_RUN%==0 net stop "%SERVICE_NAME%"
)

if exist "%SOURCE_ROOT%\%MONTH%_old" (
  echo 删除联接：%SOURCE_ROOT%\%MONTH%
  if %DRY_RUN%==0 rmdir "%SOURCE_ROOT%\%MONTH%"
  echo 还原旧目录：%SOURCE_ROOT%\%MONTH%_old -> %MONTH%
  if %DRY_RUN%==0 ren "%SOURCE_ROOT%\%MONTH%_old" "%MONTH%"
) else (
  echo 未找到 _old 目录，将从目标复制回源（耗时较长）。
  call :PickTarget %MONTH% TARGET DRIVE
  if "%TARGET%"=="" (
    echo 无法确定目标目录，回滚中止。
    goto :end
  )
  if %DRY_RUN%==0 rmdir "%SOURCE_ROOT%\%MONTH%" >nul 2>nul
  set "CMD=robocopy \"%TARGET%\%MONTH%\" \"%SOURCE_ROOT%\%MONTH%\" /MIR /COPY:DATSO /DCOPY:DAT /R:%ROBO_RETRY% /W:%ROBO_WAIT% /MT:%THREADS% /FFT /XJ /TEE /LOG+:\"%LOG_DIR%\rollback_%MONTH%.log\""
  echo [ROLLBACK] !CMD!
  if %DRY_RUN%==0 call cmd /c "!CMD!"
)

REM 启服务（可选）
if not "%SERVICE_NAME%"=="" (
  echo 尝试启动服务：%SERVICE_NAME%
  if %DRY_RUN%==0 net start "%SERVICE_NAME%"
)

:end
exit /b 0

:PickTarget
REM %1=MONTH 输出：TARGET, DRIVE(E/F)
setlocal ENABLEDELAYEDEXPANSION
set "m=%~1"
set "drive="
set "target="

call :FreeGB E: egb
call :FreeGB F: fgb

if /I "%STRATEGY%"=="BALANCE_BY_MONTH" (
  for /f "tokens=1,2 delims=-" %%a in ("!m!") do set mm=%%b
  set /a mod=!mm! %% 2
  if !mod! EQU 1 (
    if !egb! GTR %TARGET_E_MIN_FREE_GB% (set drive=E: & set target=%TARGET_E%) else if !fgb! GTR %TARGET_F_MIN_FREE_GB% (set drive=F: & set target=%TARGET_F%)
  ) else (
    if !fgb! GTR %TARGET_F_MIN_FREE_GB% (set drive=F: & set target=%TARGET_F%) else if !egb! GTR %TARGET_E_MIN_FREE_GB% (set drive=E: & set target=%TARGET_E%)
  )
) else (
  if !egb! GTR %TARGET_E_MIN_FREE_GB% (set drive=E: & set target=%TARGET_E%) else if !fgb! GTR %TARGET_F_MIN_FREE_GB% (set drive=F: & set target=%TARGET_F%)
)

endlocal & set "%2=%target%" & set "%3=%drive%"
exit /b 0

:FreeGB
REM %1=Drive(如 E:), %2=变量名
for /f "tokens=2 delims==" %%A in ('wmic logicaldisk where "DeviceID='%1'" get FreeSpace /value ^| find "FreeSpace"') do set fb=%%A
set /a gb=%fb%/1024/1024/1024
set "%2=%gb%"
exit /b 0
```

### 4.6 50-autolink-schedule.bat（每月自动联接，可选）

```bat
@echo off
setlocal ENABLEDELAYEDEXPANSION
call "%~dp0\01-config.bat"

set "CREATE_LINK=%~dp0create_current_month_link.bat"
>
"%CREATE_LINK%" (
  echo @echo off
  echo setlocal ENABLEDELAYEDEXPANSION
  echo call "%%~dp0\01-config.bat"
  echo for /f "delims=" %%%%d in ('powershell -NoProfile -Command ^(Get-Date^).ToString^('yyyy-MM'^)') do set MM=%%%%d
  echo rem 计算 E/F 可用空间(GB)
  echo for /f "tokens=2 delims==" %%%%A in ('wmic logicaldisk where "DeviceID='E:'" get FreeSpace /value ^^^| find "FreeSpace"') do set EFB=%%%%A
  echo for /f "tokens=2 delims==" %%%%A in ('wmic logicaldisk where "DeviceID='F:'" get FreeSpace /value ^^^| find "FreeSpace"') do set FFB=%%%%A
  echo set /a EGB=%%EFB%%/1024/1024/1024
  echo set /a FGB=%%FFB%%/1024/1024/1024
  echo set "TARGET="
  echo if /I "%%STRATEGY%%"=="BALANCE_BY_MONTH" ^(
  echo   for /f "tokens=1,2 delims=-" %%%%a in ("%%MM%%") do set m2=%%%%b
  echo   set /a mod=%%m2%% %% 2
  echo   if %%mod%% EQU 1 ^( if %%EGB%% GTR %%TARGET_E_MIN_FREE_GB%% ^(set "TARGET=%%TARGET_E%%"^) else if %%FGB%% GTR %%TARGET_F_MIN_FREE_GB%% ^(set "TARGET=%%TARGET_F%%"^) ^) else ^( if %%FGB%% GTR %%TARGET_F_MIN_FREE_GB%% ^(set "TARGET=%%TARGET_F%%"^) else if %%EGB%% GTR %%TARGET_E_MIN_FREE_GB%% ^(set "TARGET=%%TARGET_E%%"^) ^)
  echo ^) else ^(
  echo   if %%EGB%% GTR %%TARGET_E_MIN_FREE_GB%% ^(set "TARGET=%%TARGET_E%%"^) else if %%FGB%% GTR %%TARGET_F_MIN_FREE_GB%% ^(set "TARGET=%%TARGET_F%%"^)
  echo ^)
  echo if "%%TARGET%%"=="" ^( echo 无可用目标盘 ^& exit /b 1 ^)
  echo if not exist "%%TARGET%%\%%MM%%" mkdir "%%TARGET%%\%%MM%%"
  echo mklink /J "%%SOURCE_ROOT%%\%%MM%%" "%%TARGET%%\%%MM%%"
  echo exit /b 0
)

echo 已生成：%CREATE_LINK%

echo 创建每月计划任务（每月1日 00:05，最高权限）：
schtasks /Create /TN "CreateMonthlyLink-DataPkg" /TR "\"%CREATE_LINK%\"" /SC MONTHLY /D 1 /ST 00:05 /RL HIGHEST /F

echo 完成。可在 计划任务程序 中检查任务条目。
exit /b 0
```

### 4.7 60-run-all.bat（一键编排）

```bat
@echo off
setlocal ENABLEDELAYEDEXPANSION
call "%~dp0\01-config.bat"

REM 第1步：列出月份并准备清单
call "%~dp0\10-list-months.bat" || (echo 生成月份清单失败 & exit /b 1)

echo.
echo ========= 第2步：预同步（可重复执行，多次运行提升增量效率） =========
call "%~dp0\20-pre-sync.bat" || (echo 预同步失败，请检查日志 %LOG_DIR% & exit /b 1)

echo.
echo ========= 第3步：准备维护窗口 =========
echo - 请确认已通知业务方，准备停机。
echo - 当前为 DRY_RUN=%DRY_RUN% （1=演练，0=执行）。
choice /M "是否继续执行切换脚本 (Y/N)?"
if errorlevel 2 (echo 已取消 & exit /b 0)

REM 第4步：执行切换
call "%~dp0\30-cutover-month.bat" || (echo 切换失败，请检查日志 %LOG_DIR% & exit /b 1)

echo.
echo ========= 完成 =========
echo - 请检查应用功能、日志与磁盘空间。
echo - 若稳定运行数日后，可将 DELETE_OLD=1 再次运行切换脚本以清理 _old 目录。
exit /b 0
```

---

## 5. 傻瓜式操作步骤（图文流程）

- 步骤 A（准备）
  1) 以管理员身份登录服务器。
  2) 确认当前磁盘空间：`fsutil volume diskfree E:`、`fsutil volume diskfree F:`。
  3) 确认备份任务（从 D 到 G）继续有效，且未使用 `/SL`。

- 步骤 B（部署脚本）
  1) 新建 `C:\DataPkg-Migrate\bin\` 与 `C:\DataPkg-Migrate\logs\`。
  2) 将上文各 .bat 内容分别保存到 `bin` 目录中。

- 步骤 C（配置）
  1) 打开 `01-config.bat`，按需调整：
     - `SERVICE_NAME=Tomcat8.5`（若服务名不同请改为实际值；不确定则置空、手动停启）。
     - `STRATEGY` 设为 `FILL_E_THEN_F`（先用 E，空间紧张再用 F）或 `BALANCE_BY_MONTH`（奇偶月均衡）。
     - `TARGET_E_MIN_FREE_GB`、`TARGET_F_MIN_FREE_GB` 设置安全阈值（建议保留 ≥10-15%）。
     - 初期务必保持 `DRY_RUN=1`（演练）。

- 步骤 D（生成月份清单）
  1) 运行 `10-list-months.bat`。正常会生成：
     - `months.txt`：所有 yyyy-MM 目录列表。
     - `months_to_migrate.txt`：排除最近 2 个月后的迁移清单（如 PowerShell 不可用则需人工编辑）。
  2) 打开 `months_to_migrate.txt`，删改为计划迁移的月份（建议先挑 1 个小月试点）。

- 步骤 E（预同步）
  1) 运行 `20-pre-sync.bat`。这一步不会停机，可在业务高峰之外多次运行。
  2) 如无报错，日志在 `C:\DataPkg-Migrate\logs\pre_*.log`。

- 步骤 F（维护窗口切换）
  1) 通知业务停机窗口，确认可停应用写入。
  2) 将 `DRY_RUN=0`（正式执行），可在 `01-config.bat` 修改。
  3) 执行 `30-cutover-month.bat`（或 `60-run-all.bat` 一键编排）。脚本将：
     - 停服务（若配置了 `SERVICE_NAME`）。
     - 对每个月份执行最终差异同步（/MIR）。
     - 将 `D:\DataPkgFile\yyyy-MM` 重命名为 `yyyy-MM_old`。
     - 创建联接 `D:\DataPkgFile\yyyy-MM -> E/F:\DataPkgFile\yyyy-MM`。
     - 记录验证日志（robocopy /L 无差异即视为一致；还可抽样哈希）。
     - 保留 `_old` 目录，或按 `DELETE_OLD=1` 直接删除。
     - 启服务（若配置了 `SERVICE_NAME`）。

- 步骤 G（验证与善后）
  1) 应用层验证：上传/下载抽样测试；查看 Tomcat 日志无错误。
  2) 磁盘验证：D 盘空间是否释放，E/F 盘增长是否合理。
  3) 备份验证：次日检查从 D 到 G 的备份日志与容量变化。
  4) 稳定 1-7 天后，再将 `DELETE_OLD=1` 执行一次 `30-cutover-month.bat`，清理 `_old` 目录释放空间。

- 步骤 H（可选：每月自动联接）
  1) 执行 `50-autolink-schedule.bat` 创建计划任务。
  2) 检查“任务计划程序”中是否出现 `CreateMonthlyLink-DataPkg`。

---

## 6. 回滚方案（两级）

- 快速回滚（仍保留 `_old` 时）：
  1) 运行 `40-rollback-month.bat YYYY-MM`。
  2) 脚本将删除 `D:\DataPkgFile\YYYY-MM` 联接，并把 `YYYY-MM_old` 还原为 `YYYY-MM`。

- 深度回滚（`_old` 已删除时）：
  1) 脚本会从 `E/F:\DataPkgFile\YYYY-MM` 回复制到 `D:\DataPkgFile\YYYY-MM`（耗时长）。
  2) 复制完成后即可移除联接、改回原目录结构。

---

## 7. 常见问题（FAQ）

- Q：执行 `mklink /J` 报“权限不足”？
  - A：以管理员身份运行 CMD；或在本地安全策略中允许创建符号链接。

- Q：预同步与最终同步的差别？
  - A：预同步 `/E` 不删除目标多余文件，适合业务运行期多次累积；最终同步 `/MIR` 会删除目标端的多余文件，只能在停机窗口执行。

- Q：Robocopy 返回码不为 0？
  - A：Robocopy 的返回码不是标准错误码。`0/1` 通常表示成功（有无复制差异）；`>=8` 常表示出错，请检查日志。

- Q：备份会受影响吗？
  - A：路径仍是 `D:\DataPkgFile`，链接透明；只需确保备份脚本不使用 `/SL`。

- Q：能否迁移当前月？
  - A：不建议；当前与上月写入频繁，容易产生差异。除非确保停写，且窗口足够。

---

## 8. 命令速查

- 查看可用空间：
  - `fsutil volume diskfree E:`
  - `wmic logicaldisk where DeviceID='E:' get Size,FreeSpace`
- 创建联接（目录联接）：
  - `mklink /J "D:\DataPkgFile\2022-07" "E:\DataPkgFile\2022-07"`
- 删除联接：
  - `rmdir "D:\DataPkgFile\2022-07"`
- 差异预览（不执行复制）：
  - `robocopy "源" "目标" /L /NJH /NJS /NP /NDL /NFL`

---

## 9. 与应用代码的对应关系（供参考）

- `FileUploadUtil` 中使用 `yyyy-MM` 作为一级目录，方案 C 正是以“月份目录”为切换粒度，业务层读写路径保持不变。
- 切换后，业务访问 `D:\DataPkgFile\yyyy-MM\...` 实际读写落在 `E/F:\DataPkgFile\yyyy-MM\...`，对应用透明。

---

## 10. 最终提醒

- 先演练：DRY_RUN=1 多跑几次，观察日志与磁盘变化。
- 先小后大：先迁移一个小月；通过后批量推进。
- 留足余量：E/F 每盘至少保留 10-15% 剩余空间。
- 保留回滚：在确认稳定运行前，务必暂不删除 `_old`。
- 记录日志：所有脚本日志集中到 `C:\DataPkg-Migrate\logs\`，便于审计与追踪。
