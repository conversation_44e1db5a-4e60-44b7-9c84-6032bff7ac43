@echo off
setlocal ENABLEDELAYEDEXPANSION
call "%~dp0\01-config.bat"

if "%~1"=="" (
  echo 用法：40-rollback-month.bat YYYY-MM
  exit /b 1
)
set "MONTH=%~1"

REM 停服务（可选）
if not "%SERVICE_NAME%"=="" (
  echo 尝试停止服务：%SERVICE_NAME%
  if %DRY_RUN%==0 net stop "%SERVICE_NAME%"
)

if exist "%SOURCE_ROOT%\%MONTH%_old" (
  echo 删除联接：%SOURCE_ROOT%\%MONTH%
  if %DRY_RUN%==0 rmdir "%SOURCE_ROOT%\%MONTH%"
  echo 还原旧目录：%SOURCE_ROOT%\%MONTH%_old -> %MONTH%
  if %DRY_RUN%==0 ren "%SOURCE_ROOT%\%MONTH%_old" "%MONTH%"
) else (
  echo 未找到 _old 目录，将从目标复制回源（耗时较长）。
  call :PickTarget %MONTH% TARGET DRIVE
  if "%TARGET%"=="" (
    echo 无法确定目标目录，回滚中止。
    goto :end
  )
  if %DRY_RUN%==0 rmdir "%SOURCE_ROOT%\%MONTH%" >nul 2>nul
  set "CMD=robocopy \"%TARGET%\%MONTH%\" \"%SOURCE_ROOT%\%MONTH%\" /MIR /COPY:DATSO /DCOPY:DAT /R:%ROBO_RETRY% /W:%ROBO_WAIT% /MT:%THREADS% /FFT /XJ /TEE /LOG+:\"%LOG_DIR%\rollback_%MONTH%.log\""
  echo [ROLLBACK] !CMD!
  if %DRY_RUN%==0 call cmd /c "!CMD!"
)

REM 启服务（可选）
if not "%SERVICE_NAME%"=="" (
  echo 尝试启动服务：%SERVICE_NAME%
  if %DRY_RUN%==0 net start "%SERVICE_NAME%"
)

:end
exit /b 0

:PickTarget
REM %1=MONTH 输出：TARGET, DRIVE(E/F)
setlocal ENABLEDELAYEDEXPANSION
set "m=%~1"
set "drive="
set "target="

call :FreeGB E: egb
call :FreeGB F: fgb

if /I "%STRATEGY%"=="BALANCE_BY_MONTH" (
  for /f "tokens=1,2 delims=-" %%a in ("!m!") do set mm=%%b
  set /a mod=!mm! %% 2
  if !mod! EQU 1 (
    if !egb! GTR %TARGET_E_MIN_FREE_GB% (set drive=E: & set target=%TARGET_E%) else if !fgb! GTR %TARGET_F_MIN_FREE_GB% (set drive=F: & set target=%TARGET_F%)
  ) else (
    if !fgb! GTR %TARGET_F_MIN_FREE_GB% (set drive=F: & set target=%TARGET_F%) else if !egb! GTR %TARGET_E_MIN_FREE_GB% (set drive=E: & set target=%TARGET_E%)
  )
) else (
  if !egb! GTR %TARGET_E_MIN_FREE_GB% (set drive=E: & set target=%TARGET_E%) else if !fgb! GTR %TARGET_F_MIN_FREE_GB% (set drive=F: & set target=%TARGET_F%)
)

endlocal & set "%2=%target%" & set "%3=%drive%"
exit /b 0

:FreeGB
REM %1=Drive(如 E:), %2=变量名
for /f "tokens=2 delims==" %%A in ('wmic logicaldisk where "DeviceID='%1'" get FreeSpace /value ^| find "FreeSpace"') do set fb=%%A
set /a gb=%fb%/1024/1024/1024
set "%2=%gb%"
exit /b 0