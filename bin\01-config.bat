@echo off
REM setlocal ENABLEDELAYEDEXPANSION - 注释掉以允许变量在调用脚本中使用
REM =================== 全局配置 ===================
REM 源与目标目录
set "SOURCE_ROOT=D:\DataPkgFile"
set "TARGET_E=E:\DataPkgFile"
set "TARGET_F=F:\DataPkgFile"

REM 脚本与日志目录
set "SCRIPT_DIR=%~dp0"
set "BASE_DIR=%SCRIPT_DIR%..\"
set "LOG_DIR=D:\DataPkg-Migrate\logs"

REM 服务名（可选）：如 Tomcat8.5；若无则留空
set "SERVICE_NAME=Tomcat8"

REM 分配策略：FILL_E_THEN_F 或 BALANCE_BY_MONTH
set "STRATEGY=BALANCE_BY_MONTH"

REM 预留容量（GB），低于该值停止向该盘写入
set "TARGET_E_MIN_FREE_GB=50"
set "TARGET_F_MIN_FREE_GB=50"

REM 预同步排除最近 N 个月
set "EXCLUDE_RECENT_MONTHS=2"

REM 多线程与重试
set "THREADS=16"
set "ROBO_RETRY=1"
set "ROBO_WAIT=2"

REM 安全开关：1=演练（只打印命令），0=执行
set "DRY_RUN=1"

REM 切换后是否删除 _old：1=删除，0=保留
set "DELETE_OLD=0"

REM 抽样验证（每月抽 N 个文件哈希校验，可设 0 跳过）
set "VERIFY_SAMPLE_COUNT=5"

if not exist "%LOG_DIR%" mkdir "%LOG_DIR%" >nul 2>nul
exit /b 0